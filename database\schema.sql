-- Database schema cho hệ thống bán nội thất
-- Database: furniture_store

-- Tạo database
CREATE DATABASE IF NOT EXISTS furniture_store;
USE furniture_store;

-- Bảng users
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(50),
    last_name VA<PERSON>HA<PERSON>(50),
    phone VARCHAR(20),
    address TEXT,
    role VARCHAR(20) DEFAULT 'customer',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng categories
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INTEGER REFERENCES categories(id),
    image_url VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng products
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    sale_price DECIMAL(10,2),
    category_id INTEGER REFERENCES categories(id),
    brand VARCHAR(100),
    material VARCHAR(100),
    dimensions TEXT,
    weight DECIMAL(8,2),
    stock_quantity INTEGER DEFAULT 0,
    min_stock_level INTEGER DEFAULT 5,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng product_images
CREATE TABLE product_images (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    image_url VARCHAR(255) NOT NULL,
    alt_text VARCHAR(200),
    is_primary BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng orders
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    total_amount DECIMAL(10,2) NOT NULL,
    shipping_address TEXT,
    billing_address TEXT,
    payment_method VARCHAR(50),
    payment_status VARCHAR(50) DEFAULT 'pending',
    shipping_method VARCHAR(50),
    shipping_cost DECIMAL(8,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng order_items
CREATE TABLE order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
    product_id INTEGER REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng cart
CREATE TABLE cart (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    product_id INTEGER REFERENCES products(id),
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng detection_history
CREATE TABLE detection_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    image_url VARCHAR(255),
    detection_result JSONB,
    detected_objects_count INTEGER,
    processing_time DECIMAL(8,3),
    method VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng product_recommendations
CREATE TABLE product_recommendations (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    product_id INTEGER REFERENCES products(id),
    detected_object_class VARCHAR(100),
    confidence_score DECIMAL(5,4),
    recommendation_reason TEXT,
    is_viewed BOOLEAN DEFAULT false,
    is_purchased BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes để tối ưu performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_order_items_order ON order_items(order_id);
CREATE INDEX idx_cart_user ON cart(user_id);
CREATE INDEX idx_detection_history_user ON detection_history(user_id);
CREATE INDEX idx_product_recommendations_user ON product_recommendations(user_id);

-- Triggers để tự động cập nhật updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cart_updated_at BEFORE UPDATE ON cart
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert dữ liệu mẫu
INSERT INTO categories (name, description) VALUES
('Ghế', 'Các loại ghế sofa, ghế ăn, ghế làm việc'),
('Bàn', 'Bàn ăn, bàn làm việc, bàn trà'),
('Tủ', 'Tủ quần áo, tủ bếp, tủ trang trí'),
('Giường', 'Giường ngủ, giường tầng, giường sofa'),
('Đèn', 'Đèn bàn, đèn treo, đèn trang trí'),
('Thảm', 'Thảm trải sàn, thảm trang trí');

-- Insert sản phẩm mẫu
INSERT INTO products (name, description, price, category_id, brand, material, stock_quantity) VALUES
('Ghế Sofa Gỗ', 'Ghế sofa gỗ tự nhiên, thiết kế hiện đại', 2500000, 1, 'Furniture Pro', 'Gỗ sồi', 10),
('Bàn Ăn 6 Người', 'Bàn ăn gỗ cao cấp, phù hợp gia đình 6 người', 3500000, 2, 'Home Style', 'Gỗ xoan đào', 5),
('Tủ Quần Áo 3 Cánh', 'Tủ quần áo 3 cánh, nhiều ngăn tổ chức', 4200000, 3, 'Living Space', 'Gỗ công nghiệp', 8),
('Giường Ngủ Queen', 'Giường ngủ size Queen, thiết kế đơn giản', 1800000, 4, 'Bedroom Plus', 'Gỗ thông', 12),
('Đèn Bàn LED', 'Đèn bàn LED điều chỉnh độ sáng', 450000, 5, 'Light Pro', 'Nhôm + LED', 25),
('Thảm Trải Sàn', 'Thảm trải sàn cao cấp, họa tiết hoa văn', 1200000, 6, 'Carpet World', 'Sợi tổng hợp', 15);
