version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: furniture_store
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - furniture_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - furniture_network

  # RabbitMQ Message Queue
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: password123
    networks:
      - furniture_network

  # AI Service
  ai-service:
    build: ./ai-service
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=********************************************/furniture_store
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - furniture_network
    volumes:
      - ./ai-service/models:/app/models
      - ./ai-service/uploads:/app/uploads

  # Product Service
  product-service:
    build: ./product-service
    ports:
      - "8002:8002"
    environment:
      - DATABASE_URL=********************************************/furniture_store
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - furniture_network

  # User Service
  user-service:
    build: ./user-service
    ports:
      - "8003:8003"
    environment:
      - DATABASE_URL=********************************************/furniture_store
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-secret-key-here
    depends_on:
      - postgres
      - redis
    networks:
      - furniture_network

  # Order Service
  order-service:
    build: ./order-service
    ports:
      - "8004:8004"
    environment:
      - DATABASE_URL=********************************************/furniture_store
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://admin:password123@rabbitmq:5672
    depends_on:
      - postgres
      - redis
      - rabbitmq
    networks:
      - furniture_network

  # API Gateway
  api-gateway:
    build: ./api-gateway
    ports:
      - "8000:8000"
    environment:
      - AI_SERVICE_URL=http://ai-service:8001
      - PRODUCT_SERVICE_URL=http://product-service:8002
      - USER_SERVICE_URL=http://user-service:8003
      - ORDER_SERVICE_URL=http://order-service:8004
    depends_on:
      - ai-service
      - product-service
      - user-service
      - order-service
    networks:
      - furniture_network

  # Web Frontend
  web-frontend:
    build: ./web-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - api-gateway
    networks:
      - furniture_network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - furniture_network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - furniture_network

volumes:
  postgres_data:
  grafana_data:

networks:
  furniture_network:
    driver: bridge
