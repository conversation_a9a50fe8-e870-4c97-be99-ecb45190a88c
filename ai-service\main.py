from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from pathlib import Path
import aiofiles
import uuid
from datetime import datetime

from services.object_detection import ObjectDetectionService
from services.image_processor import ImageProcessor
from models.detection_result import DetectionResult
from database.connection import get_database_connection

app = FastAPI(
    title="AI Service - Furniture Detection",
    description="Service nhận diện đồ nội thất từ ảnh sử dụng YOLO",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Khởi tạo services
object_detection_service = ObjectDetectionService()
image_processor = ImageProcessor()

@app.get("/")
async def root():
    return {"message": "AI Service - Furniture Detection API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/detect", response_model=DetectionResult)
async def detect_furniture(file: UploadFile = File(...)):
    """
    Nhận diện đồ nội thất từ ảnh được upload
    """
    try:
        # Kiểm tra file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="Chỉ chấp nhận file ảnh")
        
        # Tạo tên file unique
        file_extension = Path(file.filename).suffix
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = f"uploads/{unique_filename}"
        
        # Lưu file
        async with aiofiles.open(file_path, 'wb') as out_file:
            content = await file.read()
            await out_file.write(content)
        
        # Xử lý ảnh và nhận diện
        detection_result = await object_detection_service.detect_objects(file_path)
        
        # Xóa file tạm
        os.remove(file_path)
        
        return detection_result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi xử lý ảnh: {str(e)}")

@app.post("/detect-slice")
async def detect_furniture_slice(file: UploadFile = File(...)):
    """
    Nhận diện đồ nội thất sử dụng kỹ thuật slicing (từ YoloSlicing)
    """
    try:
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="Chỉ chấp nhận file ảnh")
        
        file_extension = Path(file.filename).suffix
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = f"uploads/{unique_filename}"
        
        async with aiofiles.open(file_path, 'wb') as out_file:
            content = await file.read()
            await out_file.write(content)
        
        # Sử dụng kỹ thuật slicing để nhận diện
        detection_result = await object_detection_service.detect_objects_slice(file_path)
        
        os.remove(file_path)
        
        return detection_result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi xử lý ảnh: {str(e)}")

@app.get("/models")
async def list_models():
    """
    Liệt kê các model AI có sẵn
    """
    models_dir = Path("models")
    if not models_dir.exists():
        return {"models": []}
    
    models = [f.name for f in models_dir.iterdir() if f.is_file() and f.suffix in ['.pt', '.pth']]
    return {"models": models}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
