# Database Configuration
DATABASE_URL=postgresql://admin:password123@localhost:5432/furniture_store
POSTGRES_DB=furniture_store
POSTGRES_USER=admin
POSTGRES_PASSWORD=password123

# Redis Configuration
REDIS_URL=redis://localhost:6379

# RabbitMQ Configuration
RABBITMQ_URL=amqp://admin:password123@localhost:5672
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=password123

# JWT Configuration
JWT_SECRET=your-secret-key-here-change-in-production
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_EXPIRES_IN=7d

# Service URLs
AI_SERVICE_URL=http://ai-service:8001
PRODUCT_SERVICE_URL=http://product-service:8002
USER_SERVICE_URL=http://user-service:8003
ORDER_SERVICE_URL=http://order-service:8004

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000

# Monitoring
GF_SECURITY_ADMIN_PASSWORD=admin

# AI Model Configuration
YOLO_MODEL_PATH=/app/models/yolov8n.pt
CONFIDENCE_THRESHOLD=0.5
IOU_THRESHOLD=0.45

# File Upload Configuration
MAX_FILE_SIZE=10MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,bmp,tiff

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Payment Configuration (Optional)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Security
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
