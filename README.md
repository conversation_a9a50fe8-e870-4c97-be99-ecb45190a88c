# Trang Web Bán Nội Thất Tích <PERSON>ợp AI

## <PERSON><PERSON> tả dự án
Dự án xây dựng trang web bán nội thất với tính năng nhận diện sản phẩm từ ảnh sử dụng AI. Kh<PERSON>ch hàng có thể tải ảnh căn phòng, hệ thống sẽ tự động nhận diện các món đồ nội thất và gợi ý sản phẩm tương tự.

## Kiến trúc Microservices
- **AI Service**: Xử lý nhận diện vật thể sử dụng YOLO/RF-DETR
- **Product Service**: Quản lý danh mục sản phẩm
- **User Service**: Quản lý người dùng và xác thực
- **Order Service**: Quản lý đơn hàng và thanh toán
- **Web Frontend**: Giao diện người dùng
- **API Gateway**: <PERSON>ổng kết nối các service
- **Shared Libraries**: <PERSON><PERSON><PERSON> viện dùng chung

## Công nghệ sử dụng
- **Backend**: Python FastAPI, Node.js Express
- **Frontend**: React.js, TypeScript
- **Database**: PostgreSQL, Redis
- **AI Model**: YOLOv11, RF-DETR
- **Container**: Docker, Docker Compose
- **Message Queue**: RabbitMQ
- **Monitoring**: Prometheus, Grafana

## Cách chạy dự án
1. Cài đặt Docker và Docker Compose
2. Clone repository
3. Chạy `docker-compose up -d`
4. Truy cập http://localhost:3000

## Cấu trúc thư mục
```
├── ai-service/           # Service AI nhận diện vật thể
├── product-service/      # Service quản lý sản phẩm
├── user-service/         # Service quản lý người dùng
├── order-service/        # Service quản lý đơn hàng
├── web-frontend/         # Giao diện web
├── api-gateway/          # API Gateway
├── shared-libs/          # Thư viện dùng chung
├── docker-compose.yml    # Cấu hình Docker
└── docs/                 # Tài liệu dự án
```
