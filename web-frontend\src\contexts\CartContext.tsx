import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { cartAPI } from '../services/api';
import { useAuth } from './AuthContext';
import toast from 'react-hot-toast';

interface CartItem {
  id: number;
  product_id: number;
  quantity: number;
  name: string;
  price: number;
  sale_price?: number;
  current_price: number;
  image_url?: string;
  item_total: number;
  stock_quantity: number;
}

interface CartSummary {
  total_items: number;
  subtotal: number;
  tax: number;
  total: number;
}

interface Cart {
  items: CartItem[];
  summary: CartSummary;
}

interface CartContextType {
  cart: Cart | null;
  loading: boolean;
  addToCart: (productId: number, quantity: number) => Promise<void>;
  updateCartItem: (itemId: number, quantity: number) => Promise<void>;
  removeFromCart: (itemId: number) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
  getCartCount: () => Promise<number>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

interface CartProviderProps {
  children: ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [cart, setCart] = useState<Cart | null>(null);
  const [loading, setLoading] = useState(false);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated) {
      refreshCart();
    } else {
      setCart(null);
    }
  }, [isAuthenticated]);

  const refreshCart = async () => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      const response = await cartAPI.get('/cart');
      setCart(response.data);
    } catch (error: any) {
      console.error('Error fetching cart:', error);
      if (error.response?.status !== 401) {
        toast.error('Failed to load cart');
      }
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (productId: number, quantity: number) => {
    if (!isAuthenticated) {
      toast.error('Please login to add items to cart');
      return;
    }

    try {
      setLoading(true);
      await cartAPI.post('/cart/items', {
        product_id: productId,
        quantity
      });
      
      await refreshCart();
      toast.success('Item added to cart');
    } catch (error: any) {
      const message = error.response?.data?.error || 'Failed to add item to cart';
      toast.error(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateCartItem = async (itemId: number, quantity: number) => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      await cartAPI.put(`/cart/items/${itemId}`, { quantity });
      await refreshCart();
      toast.success('Cart updated');
    } catch (error: any) {
      const message = error.response?.data?.error || 'Failed to update cart';
      toast.error(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const removeFromCart = async (itemId: number) => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      await cartAPI.delete(`/cart/items/${itemId}`);
      await refreshCart();
      toast.success('Item removed from cart');
    } catch (error: any) {
      const message = error.response?.data?.error || 'Failed to remove item';
      toast.error(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const clearCart = async () => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      await cartAPI.delete('/cart');
      await refreshCart();
      toast.success('Cart cleared');
    } catch (error: any) {
      const message = error.response?.data?.error || 'Failed to clear cart';
      toast.error(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const getCartCount = async (): Promise<number> => {
    if (!isAuthenticated) return 0;

    try {
      const response = await cartAPI.get('/cart/count');
      return response.data.total_items;
    } catch (error) {
      console.error('Error getting cart count:', error);
      return 0;
    }
  };

  const value: CartContextType = {
    cart,
    loading,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    refreshCart,
    getCartCount,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
