const express = require('express');
const router = express.Router();
const { pool, redisClient } = require('../config/database');

// GET /api/search - Tìm kiếm sản phẩm
router.get('/', async (req, res) => {
  try {
    const { 
      q: searchQuery,
      category_id,
      brand,
      min_price,
      max_price,
      page = 1,
      limit = 20,
      sort_by = 'relevance',
      sort_order = 'DESC'
    } = req.query;

    if (!searchQuery || searchQuery.trim().length === 0) {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const offset = (page - 1) * limit;
    
    // Build search query with full-text search
    let query = `
      SELECT p.*, c.name as category_name,
             (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = true LIMIT 1) as primary_image,
             ts_rank(to_tsvector('english', p.name || ' ' || COALESCE(p.description, '') || ' ' || COALESCE(p.brand, '')), 
                     plainto_tsquery('english', $1)) as relevance_score
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_active = true
      AND (
        to_tsvector('english', p.name || ' ' || COALESCE(p.description, '') || ' ' || COALESCE(p.brand, '')) 
        @@ plainto_tsquery('english', $1)
        OR p.name ILIKE $2
        OR p.description ILIKE $2
        OR p.brand ILIKE $2
      )
    `;
    
    const queryParams = [searchQuery, `%${searchQuery}%`];
    let paramIndex = 3;

    // Add filters
    if (category_id) {
      query += ` AND p.category_id = $${paramIndex}`;
      queryParams.push(category_id);
      paramIndex++;
    }

    if (brand) {
      query += ` AND p.brand ILIKE $${paramIndex}`;
      queryParams.push(`%${brand}%`);
      paramIndex++;
    }

    if (min_price) {
      query += ` AND p.price >= $${paramIndex}`;
      queryParams.push(min_price);
      paramIndex++;
    }

    if (max_price) {
      query += ` AND p.price <= $${paramIndex}`;
      queryParams.push(max_price);
      paramIndex++;
    }

    // Add sorting
    if (sort_by === 'relevance') {
      query += ` ORDER BY relevance_score DESC, p.created_at DESC`;
    } else {
      const allowedSortFields = ['name', 'price', 'created_at'];
      const sortField = allowedSortFields.includes(sort_by) ? sort_by : 'created_at';
      const sortDirection = sort_order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
      query += ` ORDER BY p.${sortField} ${sortDirection}`;
    }

    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(limit, offset);

    // Get total count for the same search
    let countQuery = `
      SELECT COUNT(*) as total
      FROM products p
      WHERE p.is_active = true
      AND (
        to_tsvector('english', p.name || ' ' || COALESCE(p.description, '') || ' ' || COALESCE(p.brand, '')) 
        @@ plainto_tsquery('english', $1)
        OR p.name ILIKE $2
        OR p.description ILIKE $2
        OR p.brand ILIKE $2
      )
    `;
    
    const countParams = [searchQuery, `%${searchQuery}%`];
    let countParamIndex = 3;

    if (category_id) {
      countQuery += ` AND p.category_id = $${countParamIndex}`;
      countParams.push(category_id);
      countParamIndex++;
    }

    if (brand) {
      countQuery += ` AND p.brand ILIKE $${countParamIndex}`;
      countParams.push(`%${brand}%`);
      countParamIndex++;
    }

    if (min_price) {
      countQuery += ` AND p.price >= $${countParamIndex}`;
      countParams.push(min_price);
      countParamIndex++;
    }

    if (max_price) {
      countQuery += ` AND p.price <= $${countParamIndex}`;
      countParams.push(max_price);
      countParamIndex++;
    }

    const [searchResult, countResult] = await Promise.all([
      pool.query(query, queryParams),
      pool.query(countQuery, countParams)
    ]);

    const products = searchResult.rows;
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      query: searchQuery,
      products,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error searching products:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/search/suggestions - Gợi ý tìm kiếm
router.get('/suggestions', async (req, res) => {
  try {
    const { q: searchQuery, limit = 10 } = req.query;

    if (!searchQuery || searchQuery.trim().length < 2) {
      return res.json({ suggestions: [] });
    }

    // Check cache first
    const cacheKey = `search_suggestions:${searchQuery.toLowerCase()}:${limit}`;
    const cachedSuggestions = await redisClient.get(cacheKey);
    
    if (cachedSuggestions) {
      return res.json(JSON.parse(cachedSuggestions));
    }

    const query = `
      SELECT DISTINCT name, brand
      FROM products
      WHERE is_active = true
      AND (name ILIKE $1 OR brand ILIKE $1)
      ORDER BY 
        CASE 
          WHEN name ILIKE $2 THEN 1
          WHEN name ILIKE $1 THEN 2
          WHEN brand ILIKE $2 THEN 3
          ELSE 4
        END,
        name
      LIMIT $3
    `;

    const result = await pool.query(query, [
      `%${searchQuery}%`,
      `${searchQuery}%`,
      limit
    ]);

    const suggestions = [];
    
    result.rows.forEach(row => {
      if (row.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        suggestions.push({
          text: row.name,
          type: 'product'
        });
      }
      if (row.brand && row.brand.toLowerCase().includes(searchQuery.toLowerCase()) && 
          !suggestions.find(s => s.text === row.brand)) {
        suggestions.push({
          text: row.brand,
          type: 'brand'
        });
      }
    });

    // Cache for 15 minutes
    await redisClient.setEx(cacheKey, 900, JSON.stringify({ suggestions }));

    res.json({ suggestions });

  } catch (error) {
    console.error('Error getting search suggestions:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/search/similar/:id - Tìm sản phẩm tương tự
router.get('/similar/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { limit = 10 } = req.query;

    // Check cache first
    const cacheKey = `similar_products:${id}:${limit}`;
    const cachedSimilar = await redisClient.get(cacheKey);
    
    if (cachedSimilar) {
      return res.json(JSON.parse(cachedSimilar));
    }

    // Get the product details first
    const productQuery = `
      SELECT category_id, brand, price, name
      FROM products
      WHERE id = $1 AND is_active = true
    `;

    const productResult = await pool.query(productQuery, [id]);

    if (productResult.rows.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    const product = productResult.rows[0];

    // Find similar products based on category, brand, and price range
    const similarQuery = `
      SELECT p.*, c.name as category_name,
             (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = true LIMIT 1) as primary_image,
             CASE 
               WHEN p.category_id = $2 AND p.brand = $3 THEN 1
               WHEN p.category_id = $2 THEN 2
               WHEN p.brand = $3 THEN 3
               ELSE 4
             END as similarity_score
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.id != $1 
      AND p.is_active = true
      AND (
        p.category_id = $2 
        OR p.brand = $3
        OR (p.price BETWEEN $4 AND $5)
      )
      ORDER BY similarity_score, ABS(p.price - $6), p.created_at DESC
      LIMIT $7
    `;

    const priceRange = product.price * 0.3; // 30% price range
    const minPrice = product.price - priceRange;
    const maxPrice = product.price + priceRange;

    const similarResult = await pool.query(similarQuery, [
      id,
      product.category_id,
      product.brand,
      minPrice,
      maxPrice,
      product.price,
      limit
    ]);

    const similarProducts = similarResult.rows;

    // Cache for 2 hours
    await redisClient.setEx(cacheKey, 7200, JSON.stringify({ similarProducts }));

    res.json({ similarProducts });

  } catch (error) {
    console.error('Error finding similar products:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/search/popular - Sản phẩm phổ biến
router.get('/popular', async (req, res) => {
  try {
    const { limit = 20, category_id } = req.query;

    // Check cache first
    const cacheKey = `popular_products:${category_id || 'all'}:${limit}`;
    const cachedPopular = await redisClient.get(cacheKey);
    
    if (cachedPopular) {
      return res.json(JSON.parse(cachedPopular));
    }

    let query = `
      SELECT p.*, c.name as category_name,
             (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = true LIMIT 1) as primary_image,
             COALESCE(order_count, 0) as order_count
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN (
        SELECT product_id, COUNT(*) as order_count
        FROM order_items oi
        JOIN orders o ON oi.order_id = o.id
        WHERE o.status IN ('completed', 'shipped', 'delivered')
        AND o.created_at >= NOW() - INTERVAL '30 days'
        GROUP BY product_id
      ) popular ON p.id = popular.product_id
      WHERE p.is_active = true
    `;

    const queryParams = [];
    let paramIndex = 1;

    if (category_id) {
      query += ` AND p.category_id = $${paramIndex}`;
      queryParams.push(category_id);
      paramIndex++;
    }

    query += ` ORDER BY order_count DESC, p.created_at DESC LIMIT $${paramIndex}`;
    queryParams.push(limit);

    const result = await pool.query(query, queryParams);
    const popularProducts = result.rows;

    // Cache for 1 hour
    await redisClient.setEx(cacheKey, 3600, JSON.stringify({ popularProducts }));

    res.json({ popularProducts });

  } catch (error) {
    console.error('Error getting popular products:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
